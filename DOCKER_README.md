# RedAI MCP Servers Docker Deployment

Hướng dẫn triển khai các RedAI MCP Servers sử dụng Docker và Docker Compose.

## 📋 Tổng quan

Dự án này bao gồm 6 MCP servers được containerized:

| Service | Port | Description |
|---------|------|-------------|
| **Affiliate** | 8004 | Quản lý hệ thống affiliate |
| **Blog** | 8005 | Quản lý hệ thống blog |
| **Model** | 8006 | Quản lý models AI và fine-tuning |
| **Data** | 8007 | Quản lý dữ liệu người dùng |
| **Marketplace** | 8008 | Quản lý marketplace |
| **Shipment** | 8009 | Quản lý vận chuyển (GHN, GHTK, J&T, AhaMove) |

## 🚀 Quick Start

### 1. Chuẩn bị môi trường

```bash
# Clone repository
git clone <repository-url>
cd redai-v201-mcp-server

# Copy environment file
cp .env.docker.example .env

# Edit environment variables
nano .env  # hoặc editor khác
```

### 2. Build và chạy tất cả services

```bash
# Build all Docker images
./docker-build.sh

# Start all services
./docker-manage.sh start
```

### 3. Kiểm tra trạng thái

```bash
# Check service status
./docker-manage.sh status

# View logs
./docker-manage.sh logs
```

## 🔧 Quản lý Services

### Sử dụng Management Script

```bash
# Start all services
./docker-manage.sh start

# Stop all services
./docker-manage.sh stop

# Restart all services
./docker-manage.sh restart

# Show status
./docker-manage.sh status

# Show logs (all services)
./docker-manage.sh logs

# Show logs (specific service)
./docker-manage.sh logs redai-affiliate

# Build images
./docker-manage.sh build

# Clean up (remove containers and images)
./docker-manage.sh clean
```

### Sử dụng Docker Compose trực tiếp

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs -f

# Scale specific service
docker-compose up -d --scale redai-affiliate=2

# Rebuild and restart
docker-compose up -d --build
```

## 🔧 Cấu hình

### Environment Variables

Chỉnh sửa file `.env` với các giá trị thực tế:

```bash
# Global configuration
REDAI_API_BASE_URL=https://api.redai.com

# Service-specific configuration
REDAI_AFFILIATE_API_BASE_URL=https://api.redai.com
AFFILIATE_HTTP_HOST=0.0.0.0
AFFILIATE_HTTP_PORT=8004

# Shipment API keys
GHN_TOKEN=your_actual_ghn_token
GHN_SHOP_ID=your_actual_shop_id
GHTK_TOKEN=your_actual_ghtk_token
# ... other API keys
```

### Port Configuration

Mặc định các services chạy trên các ports:
- Affiliate: 8004
- Blog: 8005  
- Model: 8006
- Data: 8007
- Marketplace: 8008
- Shipment: 8009

Để thay đổi ports, chỉnh sửa trong `docker-compose.yml`:

```yaml
services:
  redai-affiliate:
    ports:
      - "9004:8004"  # External:Internal
```

## 🔍 Monitoring và Debugging

### Health Checks

Tất cả services đều có health checks tự động:

```bash
# Check health status
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Manual health check
curl -f http://localhost:8004/health
```

### Logs

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f redai-affiliate

# View logs with timestamps
docker-compose logs -f -t

# Follow last 100 lines
docker-compose logs -f --tail=100
```

### Debug Container

```bash
# Access container shell
docker exec -it redai-affiliate-server bash

# Check container processes
docker exec redai-affiliate-server ps aux

# Check container environment
docker exec redai-affiliate-server env
```

## 📊 Service URLs

Sau khi khởi động, các services sẽ có sẵn tại:

- **Affiliate Server**: http://localhost:8004/mcp
- **Blog Server**: http://localhost:8005/mcp
- **Model Server**: http://localhost:8006/mcp
- **Data Server**: http://localhost:8007/mcp
- **Marketplace Server**: http://localhost:8008/mcp
- **Shipment Server**: http://localhost:8009/mcp

## 🔒 Security

### Production Deployment

Cho production, cần cập nhật các cấu hình bảo mật:

```bash
# In .env file
SECURE_HEADERS=true
CORS_ORIGINS=https://yourdomain.com
LOG_LEVEL=WARNING
```

### SSL/TLS

Để sử dụng HTTPS, thêm reverse proxy (nginx/traefik):

```yaml
# docker-compose.override.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
```

## 🚨 Troubleshooting

### Common Issues

**1. Port already in use**
```bash
# Check what's using the port
lsof -i :8004

# Kill process or change port in docker-compose.yml
```

**2. Permission denied**
```bash
# Make scripts executable
chmod +x docker-build.sh docker-manage.sh
```

**3. Docker daemon not running**
```bash
# Start Docker service
sudo systemctl start docker
# or on macOS/Windows: start Docker Desktop
```

**4. Out of disk space**
```bash
# Clean up Docker
docker system prune -a
docker volume prune
```

### Performance Tuning

```bash
# Increase memory limits in docker-compose.yml
services:
  redai-model:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

## 📝 Development

### Local Development

```bash
# Run single service for development
docker-compose up redai-affiliate

# Mount source code for live reload
# Add to docker-compose.override.yml:
volumes:
  - ./src:/app/src
```

### Testing

```bash
# Run tests in container
docker exec redai-affiliate-server python -m pytest

# Run specific test
docker exec redai-affiliate-server python test_affiliate_server.py
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [FastMCP Documentation](https://github.com/jlowin/fastmcp)
- [Model Context Protocol](https://modelcontextprotocol.io)

## 🤝 Support

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs: `./docker-manage.sh logs`
2. Kiểm tra status: `./docker-manage.sh status`
3. Tạo issue với thông tin chi tiết về lỗi
