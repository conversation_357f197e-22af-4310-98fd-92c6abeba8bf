#!/bin/bash
# RedAI MCP Servers Docker Management Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 {start|stop|restart|status|logs|build|clean|help}"
    echo ""
    echo "Commands:"
    echo "  start    - Start all RedAI MCP servers"
    echo "  stop     - Stop all RedAI MCP servers"
    echo "  restart  - Restart all RedAI MCP servers"
    echo "  status   - Show status of all services"
    echo "  logs     - Show logs from all services"
    echo "  build    - Build all Docker images"
    echo "  clean    - Remove all containers and images"
    echo "  help     - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 logs                     # Show all logs"
    echo "  $0 logs redai-affiliate     # Show specific service logs"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start services
start_services() {
    print_status "Starting RedAI MCP Servers..."
    check_docker
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Copying from .env.docker.example..."
        cp .env.docker.example .env
        print_warning "Please edit .env file with your actual configuration before running again."
        return 1
    fi
    
    docker-compose up -d
    print_success "All services started successfully!"
    
    echo ""
    echo "📊 Service URLs:"
    echo "   Affiliate Server: http://localhost:8004/mcp"
    echo "   Blog Server: http://localhost:8005/mcp"
    echo "   Model Server: http://localhost:8006/mcp"
    echo "   Data Server: http://localhost:8007/mcp"
    echo "   Marketplace Server: http://localhost:8008/mcp"
    echo "   Shipment Server: http://localhost:8009/mcp"
}

# Function to stop services
stop_services() {
    print_status "Stopping RedAI MCP Servers..."
    check_docker
    docker-compose down
    print_success "All services stopped successfully!"
}

# Function to restart services
restart_services() {
    print_status "Restarting RedAI MCP Servers..."
    stop_services
    start_services
}

# Function to show status
show_status() {
    print_status "RedAI MCP Servers Status:"
    check_docker
    docker-compose ps
    
    echo ""
    print_status "Container Health Status:"
    docker ps --filter "name=redai-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Function to show logs
show_logs() {
    check_docker
    if [ -n "$2" ]; then
        print_status "Showing logs for $2..."
        docker-compose logs -f "$2"
    else
        print_status "Showing logs for all services..."
        docker-compose logs -f
    fi
}

# Function to build images
build_images() {
    print_status "Building Docker images..."
    ./docker-build.sh
}

# Function to clean up
clean_up() {
    print_warning "This will remove all RedAI MCP containers and images. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning up Docker containers and images..."
        check_docker
        
        # Stop and remove containers
        docker-compose down --remove-orphans
        
        # Remove images
        docker images | grep "redai-.*-server" | awk '{print $3}' | xargs -r docker rmi -f
        
        # Remove unused volumes
        docker volume prune -f
        
        print_success "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Main script logic
case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$@"
        ;;
    build)
        build_images
        ;;
    clean)
        clean_up
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
