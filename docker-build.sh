#!/bin/bash
# RedAI MCP Servers Docker Build Script

set -e

echo "🚀 Building RedAI MCP Servers Docker Images..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Create logs directories
print_status "Creating log directories..."
mkdir -p logs/{affiliate,blog,model,data,marketplace,shipment}

# Build images
services=("affiliate" "blog" "model" "data" "marketplace" "shipment")
ports=("8004" "8005" "8006" "8007" "8008" "8009")

for i in "${!services[@]}"; do
    service="${services[$i]}"
    port="${ports[$i]}"
    
    print_status "Building ${service} server image..."
    
    if [ "$service" = "affiliate" ]; then
        dockerfile_path="src/server/redai_system/affiliatte/Dockerfile"
    else
        dockerfile_path="src/server/redai_system/${service}/Dockerfile"
    fi
    
    if docker build -t "redai-${service}-server:latest" -f "$dockerfile_path" .; then
        print_success "Successfully built redai-${service}-server:latest"
    else
        print_error "Failed to build redai-${service}-server:latest"
        exit 1
    fi
done

print_success "All Docker images built successfully!"

echo ""
echo "📋 Built Images:"
docker images | grep "redai-.*-server"

echo ""
echo "🚀 To run all services:"
echo "   docker-compose up -d"
echo ""
echo "🔍 To run individual service:"
for i in "${!services[@]}"; do
    service="${services[$i]}"
    port="${ports[$i]}"
    echo "   docker run -d -p ${port}:${port} --name redai-${service} redai-${service}-server:latest"
done

echo ""
echo "📊 Service URLs:"
for i in "${!services[@]}"; do
    service="${services[$i]}"
    port="${ports[$i]}"
    echo "   ${service^} Server: http://localhost:${port}/mcp"
done
