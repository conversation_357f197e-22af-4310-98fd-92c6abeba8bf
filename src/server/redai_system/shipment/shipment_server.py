"""
Server MCP cho Shipment với API GHN (Giao hàng Nhanh) và GHTK (Giao hàng Tiết kiệm)

Server này cung cấp các công cụ và tài nguyên để tích hợp với cả hai API:
- GHN API: Tạo đơn hàng, t<PERSON>h cướ<PERSON> phí, quản lý địa chỉ và dịch vụ vận chuyển
- GHTK API: Đăng đơn, tính phí, quản lý sản phẩm và địa chỉ

Tính năng GHN:
- Tạo và quản lý đơn hàng vận chuyển
- Tính toán cước phí và thời gian giao hàng
- Quản lý địa chỉ (tỉnh/thành, quận/huyện, phường/xã)
- Hỗ trợ khách hàng qua ticket system
- Quản lý cửa hàng và nhân viên

Tính năng GHTK:
- Đăng đơn hàng và quản lý trạng thái
- <PERSON><PERSON>h phí vận chuyển và in nhãn
- Quản lý sản phẩm và địa chỉ lấy hàng
- Hỗ trợ webhook để nhận cập nhật trạng thái

Bảo mật:
- GHN: Xác thực qua Token và ShopID
- GHTK: Xác thực qua Token và Partner Code
- Hỗ trợ môi trường test và production

Cấu trúc:
- utils/: Cấu hình và HTTP client chung
- tools/: Các công cụ thực hiện hành động
- resources/: Các tài nguyên lấy dữ liệu
"""

# MCP libraries
from fastmcp import FastMCP
import os

# Import cấu hình và utils
try:
    # Thử relative imports trước (khi chạy như module)
    from .utils import (
        ShipmentEnvironment,
        GHN_TOKEN,
        GHN_SHOP_ID,
        GHN_ENVIRONMENT,
        GHN_BASE_URL,
        GHTK_TOKEN,
        GHTK_PARTNER_CODE,
        GHTK_ENVIRONMENT,
        GHTK_BASE_URL,
        JT_USERNAME,
        JT_API_KEY,
        JT_CUSTOMER_CODE,
        JT_ENVIRONMENT,
        JT_BASE_URL,
        AHAMOVE_API_KEY,
        AHAMOVE_TOKEN,
        AHAMOVE_MOBILE,
        AHAMOVE_ENVIRONMENT,
        AHAMOVE_BASE_URL,
        BASE_URL,
        make_ghn_request
    )

    # Import tools registration functions
    from .tools.ghn_tools import register_ghn_tools
    from .tools.ghtk_tools import register_ghtk_tools
    from .tools.jt_tools import register_jt_tools
    from .tools.ahamove_tools import register_ahamove_tools

    # Import resources registration functions
    from .resources.ghn_resources import register_ghn_resources
    from .resources.ghtk_resources import register_ghtk_resources
    from .resources.jt_resources import register_jt_resources
    from .resources.ahamove_resources import register_ahamove_resources

except ImportError:
    # Fallback cho absolute imports (khi chạy trực tiếp)
    import sys
    import os

    # Thêm thư mục hiện tại vào Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from utils import (
        ShipmentEnvironment,
        GHN_TOKEN,
        GHN_SHOP_ID,
        GHN_ENVIRONMENT,
        GHN_BASE_URL,
        GHTK_TOKEN,
        GHTK_PARTNER_CODE,
        GHTK_ENVIRONMENT,
        GHTK_BASE_URL,
        JT_USERNAME,
        JT_API_KEY,
        JT_CUSTOMER_CODE,
        JT_ENVIRONMENT,
        JT_BASE_URL,
        AHAMOVE_API_KEY,
        AHAMOVE_TOKEN,
        AHAMOVE_MOBILE,
        AHAMOVE_ENVIRONMENT,
        AHAMOVE_BASE_URL,
        BASE_URL,
        make_ghn_request
    )

    # Import tools registration functions
    from tools.ghn_tools import register_ghn_tools
    from tools.ghtk_tools import register_ghtk_tools
    from tools.jt_tools import register_jt_tools
    from tools.ahamove_tools import register_ahamove_tools

    # Import resources registration functions
    from resources.ghn_resources import register_ghn_resources
    from resources.ghtk_resources import register_ghtk_resources
    from resources.jt_resources import register_jt_resources
    from resources.ahamove_resources import register_ahamove_resources

# Cấu hình HTTP server
HTTP_HOST = os.getenv("GHN_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GHN_HTTP_PORT", "8000"))
HTTP_PATH = os.getenv("GHN_HTTP_PATH", "/mcp")

# Khởi tạo MCP server
mcp = FastMCP("Shipment-Server-GHN-GHTK-JT-Ahamove")

# Đăng ký tất cả GHN tools
register_ghn_tools(mcp)

# Đăng ký tất cả GHTK tools
register_ghtk_tools(mcp)

# Đăng ký tất cả J&T tools
register_jt_tools(mcp)

# Đăng ký tất cả Ahamove tools
register_ahamove_tools(mcp)

# Đăng ký tất cả GHN resources
register_ghn_resources(mcp)

# Đăng ký tất cả GHTK resources
register_ghtk_resources(mcp)

# Đăng ký tất cả J&T resources
register_jt_resources(mcp)

# Đăng ký tất cả Ahamove resources
register_ahamove_resources(mcp)

# ============================================================================
# MAIN - Khởi chạy server
# ============================================================================

def main():
    """
    Hàm main để khởi chạy MCP server
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        print("="*60)
        print("🚚 Khởi động Shipment MCP Server (GHN + GHTK + J&T + Ahamove)")
        print("="*60)
        print("📦 GHN Configuration:")
        print(f"   📍 Môi trường: {GHN_ENVIRONMENT.upper()}")
        print(f"   🌐 Base URL: {GHN_BASE_URL}")
        print(f"   🏪 Shop ID: {GHN_SHOP_ID}")
        print(f"   🔑 Token: {'*' * (len(GHN_TOKEN) - 4) + GHN_TOKEN[-4:] if GHN_TOKEN else 'Chưa cấu hình'}")
        print()
        print("📦 GHTK Configuration:")
        print(f"   📍 Môi trường: {GHTK_ENVIRONMENT.upper()}")
        print(f"   🌐 Base URL: {GHTK_BASE_URL}")
        print(f"   🏪 Partner Code: {GHTK_PARTNER_CODE}")
        print(f"   🔑 Token: {'*' * (len(GHTK_TOKEN) - 4) + GHTK_TOKEN[-4:] if GHTK_TOKEN else 'Chưa cấu hình'}")
        print()
        print("📦 J&T Configuration:")
        print(f"   📍 Môi trường: {JT_ENVIRONMENT.upper()}")
        print(f"   🌐 Base URL: {JT_BASE_URL}")
        print(f"   👤 Username: {JT_USERNAME}")
        print(f"   🏪 Customer Code: {JT_CUSTOMER_CODE}")
        print(f"   🔑 API Key: {'*' * (len(JT_API_KEY) - 4) + JT_API_KEY[-4:] if JT_API_KEY else 'Chưa cấu hình'}")
        print()
        print("📦 Ahamove Configuration:")
        print(f"   📍 Môi trường: {AHAMOVE_ENVIRONMENT.upper()}")
        print(f"   🌐 Base URL: {AHAMOVE_BASE_URL}")
        print(f"   📱 Mobile: {AHAMOVE_MOBILE}")
        print(f"   🔑 API Key: {'*' * (len(AHAMOVE_API_KEY) - 4) + AHAMOVE_API_KEY[-4:] if AHAMOVE_API_KEY else 'Chưa cấu hình'}")
        print(f"   🎫 Token: {'*' * (len(AHAMOVE_TOKEN) - 4) + AHAMOVE_TOKEN[-4:] if AHAMOVE_TOKEN else 'Chưa cấu hình'}")
        print()
        print(f"🚀 Transport: Streamable HTTP")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print(f"📋 API Documentation: http://{HTTP_HOST}:{HTTP_PORT}/docs")

        # Kiểm tra cấu hình
        has_test_config = False
        if GHN_TOKEN == "test_token_12345" or GHN_SHOP_ID == "12345":
            has_test_config = True
        if GHTK_TOKEN == "test_ghtk_token_12345" or GHTK_PARTNER_CODE == "test_partner_12345":
            has_test_config = True
        if JT_API_KEY == "test_jt_api_key_12345" or JT_USERNAME == "test_jt_username":
            has_test_config = True
        if AHAMOVE_API_KEY == "test_ahamove_api_key_12345" or AHAMOVE_TOKEN == "test_ahamove_token_12345":
            has_test_config = True

        if has_test_config:
            print("-"*60)
            print("⚠️  CẢNH BÁO: Đang sử dụng cấu hình test!")
            print("📋 Để sử dụng API thực tế:")
            print("   1. Sao chép .env.example thành .env")
            print("   2. Điền các giá trị thực tế:")
            print("      - GHN_TOKEN và GHN_SHOP_ID (cho GHN)")
            print("      - GHTK_TOKEN và GHTK_PARTNER_CODE (cho GHTK)")
            print("      - JT_USERNAME, JT_API_KEY và JT_CUSTOMER_CODE (cho J&T)")
            print("      - AHAMOVE_API_KEY, AHAMOVE_TOKEN và AHAMOVE_MOBILE (cho Ahamove)")
            print("   3. Khởi động lại server")
            print("🧪 Server vẫn có thể chạy để test cấu trúc MCP")
        print("-"*60)
        print("📋 Cấu trúc modular:")
        print("   📁 utils/ - Cấu hình và HTTP client")
        print("   📁 tools/ - Các công cụ thực hiện hành động")
        print("      ├── ghn_tools.py - GHN tools (gộp chung)")
        print("      ├── ghtk_tools.py - GHTK tools")
        print("      ├── jt_tools.py - J&T tools")
        print("      └── ahamove_tools.py - Ahamove tools")
        print("   📁 resources/ - Các tài nguyên lấy dữ liệu")
        print("      ├── ghn_resources.py - GHN resources (gộp chung)")
        print("      ├── ghtk_resources.py - GHTK resources")
        print("      ├── jt_resources.py - J&T resources")
        print("      └── ahamove_resources.py - Ahamove resources")
        print("-"*60)
        print("📋 Các tính năng có sẵn:")
        print("   🔧 GHN Tools (Công cụ GHN):")
        print("      • create_order - Tạo đơn hàng mới")
        print("      • cancel_order - Hủy đơn hàng")
        print("      • calculate_fee - Tính cước phí vận chuyển")
        print("      • calculate_leadtime - Tính thời gian giao hàng")
        print("      • preview_order - Xem trước đơn hàng")
        print("      • create_print_token - Tạo token in vận đơn")
        print("      • create_ticket - Tạo ticket hỗ trợ")
        print("      • reply_ticket - Phản hồi ticket")
        print("      • get_otp - Lấy mã OTP")
        print("      • add_staff_to_store - Thêm nhân viên vào cửa hàng")
        print()
        print("   🔧 GHTK Tools (Công cụ GHTK):")
        print("      • ghtk_get_solutions_tool - Lấy danh sách giải pháp")
        print("      • ghtk_create_order_tool - Tạo đơn hàng GHTK")
        print("      • ghtk_calculate_fee_tool - Tính phí vận chuyển")
        print("      • ghtk_get_order_status_tool - Lấy trạng thái đơn hàng")
        print("      • ghtk_print_label_tool - In nhãn đơn hàng")
        print("      • ghtk_cancel_order_tool - Hủy đơn hàng")
        print("      • ghtk_get_pick_addresses_tool - Lấy địa chỉ lấy hàng")
        print("      • ghtk_get_level4_addresses_tool - Lấy địa chỉ cấp 4")
        print("      • ghtk_search_products_tool - Tìm kiếm sản phẩm")
        print()
        print("   🔧 J&T Tools (Công cụ J&T Express):")
        print("      • jt_create_order_tool - Tạo đơn hàng J&T")
        print("      • jt_track_order_tool - Tra cứu vận đơn J&T")
        print("      • jt_calculate_tariff_tool - Tính cước vận chuyển")
        print("      • jt_cancel_order_tool - Hủy đơn hàng")
        print("      • jt_create_simple_order_tool - Tạo đơn hàng đơn giản")
        print("      • jt_get_service_info_tool - Thông tin dịch vụ")
        print()
        print("   🔧 Ahamove Tools (Công cụ Ahamove):")
        print("      • ahamove_register_account_tool - Đăng ký tài khoản")
        print("      • ahamove_authenticate_token_tool - Xác thực token")
        print("      • ahamove_get_cities_tool - Lấy danh sách thành phố")
        print("      • ahamove_get_services_tool - Lấy danh sách dịch vụ")
        print("      • ahamove_estimate_order_fee_tool - Ước tính phí")
        print("      • ahamove_create_order_tool - Tạo đơn hàng")
        print("      • ahamove_get_order_detail_tool - Chi tiết đơn hàng")
        print("      • ahamove_cancel_order_tool - Hủy đơn hàng")
        print("      • ahamove_get_order_tracking_tool - Tracking đơn hàng")
        print("      • ahamove_add_child_account_tool - Thêm tài khoản con")
        print()
        print("   📚 GHN Resources (Tài nguyên GHN):")
        print("      • ghn://order/{order_code} - Thông tin đơn hàng")
        print("      • ghn://order-by-client/{client_order_code} - Đơn hàng theo mã khách")
        print("      • ghn://provinces - Danh sách tỉnh/thành phố")
        print("      • ghn://districts/{province_id} - Danh sách quận/huyện")
        print("      • ghn://wards/{district_id} - Danh sách phường/xã")
        print("      • ghn://services/{from_district}/{to_district} - Dịch vụ vận chuyển")
        print("      • ghn://stations/{district_id} - Danh sách bưu cục")
        print("      • ghn://stores - Danh sách cửa hàng")
        print("      • ghn://pick-shifts - Ca lấy hàng")
        print("      • ghn://tickets - Danh sách ticket hỗ trợ")
        print()
        print("   📚 GHTK Resources (Tài nguyên GHTK):")
        print("      • ghtk://solutions - Danh sách giải pháp")
        print("      • ghtk://order/{tracking_order} - Thông tin đơn hàng")
        print("      • ghtk://pick-addresses - Địa chỉ lấy hàng")
        print("      • ghtk://products - Danh sách sản phẩm")
        print("      • ghtk://products/search/{term} - Tìm kiếm sản phẩm")
        print("      • ghtk://webhook-info - Thông tin webhook")
        print("      • ghtk://status-codes - Mã trạng thái đơn hàng")
        print()
        print("   📚 J&T Resources (Tài nguyên J&T Express):")
        print("      • jt://order/{awb} - Thông tin đơn hàng theo AWB")
        print("      • jt://service-info - Thông tin dịch vụ J&T")
        print("      • jt://city-codes - Danh sách mã thành phố")
        print("      • jt://api-guide - Hướng dẫn sử dụng API")
        print("      • jt://status-codes - Mã trạng thái đơn hàng")
        print()
        print("   📚 Ahamove Resources (Tài nguyên Ahamove):")
        print("      • ahamove://cities - Danh sách thành phố")
        print("      • ahamove://services/{city_id} - Dịch vụ theo thành phố")
        print("      • ahamove://service/{service_id} - Chi tiết dịch vụ")
        print("      • ahamove://order/{order_id} - Chi tiết đơn hàng")
        print("      • ahamove://order/{order_id}/tracking - Tracking đơn hàng")
        print("      • ahamove://child-accounts - Danh sách tài khoản con")
        print("      • ahamove://service-types - Thông tin loại dịch vụ")
        print("      • ahamove://payment-methods - Phương thức thanh toán")
        print("      • ahamove://order-statuses - Trạng thái đơn hàng")
        print("      • ahamove://api-guide - Hướng dẫn sử dụng API")
        print("="*60)
        print("🚀 Đang khởi động server...")

        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
