# RedAI Shipment MCP Server Dockerfile
# Sử dụng Python 3.11 slim image để giảm kích thước
FROM python:3.11-slim

# Metadata
LABEL maintainer="RedAI Development Team <<EMAIL>>"
LABEL description="RedAI Shipment MCP Server - Qu<PERSON>n lý hệ thống vận chuyển cho người dùng"
LABEL version="1.0.0"

# Thiết lập biến môi trường
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Tạo user non-root để bảo mật
RUN groupadd -r redai && useradd -r -g redai redai

# Thiết lập working directory
WORKDIR /app

# Cài đặt system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements và cài đặt Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ ./src/
COPY *.py ./

# Copy shipment-specific files
COPY src/server/redai_system/shipment/ ./src/server/redai_system/shipment/

# Tạo thư mục logs và đổi ownership
RUN mkdir -p /app/logs && \
    chown -R redai:redai /app

# Switch to non-root user
USER redai

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8009/health || exit 1

# Expose port
EXPOSE 8009

# Environment variables với default values
ENV REDAI_SHIPMENT_API_BASE_URL=https://api.redai.com \
    SHIPMENT_HTTP_HOST=0.0.0.0 \
    SHIPMENT_HTTP_PORT=8009 \
    SHIPMENT_HTTP_PATH=/mcp \
    SHIPMENT_TRANSPORT=streamable-http \
    GHN_TOKEN=test \
    GHN_SHOP_ID=test \
    GHTK_TOKEN=test \
    GHTK_PARTNER_CODE=test \
    JT_USERNAME=test \
    JT_API_KEY=test \
    JT_CUSTOMER_CODE=test \
    AHAMOVE_API_KEY=test \
    AHAMOVE_TOKEN=test \
    AHAMOVE_MOBILE=test

# Command để chạy server
CMD ["python", "src/server/redai_system/shipment/shipment_server.py"]
