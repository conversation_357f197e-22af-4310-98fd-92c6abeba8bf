"""
Simple MCP Server for RedAI Global.

Simplest possible version for testing connection.

L<PERSON>u ý:
- Chỉ chạy một máy chủ MCP với transport "stdio" tại một thời điểm
- <PERSON><PERSON><PERSON> cần chạy nhiều máy chủ, sử dụng transport khác như HTTP hoặc chạy trong các tiến trình riêng biệt
- Lỗi UnicodeDecodeError xảy ra khi cố gắng chạy nhiều máy chủ với transport "stdio" cùng lúc
"""
import json
import os
from pathlib import Path
from dotenv import load_dotenv
import httpx
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, RouteType

load_dotenv()


def read_json_file(file_path: str):
    """
    Đọc file JSON từ đường dẫn được truyền vào.

    :param file_path: Đường dẫn đến file .json
    :return: Dữ liệu dưới dạng dict hoặc list (tùy file)
    """
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"Không tìm thấy file: {file_path}")

    with path.open("r", encoding="utf-8") as f:
        return json.load(f)


def find_matching_subtrees(obj, keyword: str):
    keyword = keyword.lower()
    result = {}

    if isinstance(obj, dict):
        for key, value in obj.items():
            key_match = keyword in key.lower()
            value_match = (
                    isinstance(value, str) and keyword in value.lower()
            )

            # Đệ quy vào value nếu là dict hoặc list
            sub_result = find_matching_subtrees(value, keyword)

            # Nếu key hoặc value khớp, hoặc có sub_result thì giữ lại
            if key_match or value_match or sub_result:
                result[key] = sub_result if sub_result else value

    elif isinstance(obj, list):
        matched_list = []
        for item in obj:
            sub_result = find_matching_subtrees(item, keyword)
            if sub_result:
                matched_list.append(sub_result)
        if matched_list:
            return matched_list

    return result


def main():
    try:
        # Fix encoding issues on Windows
        print("Reading schema file...")
        schema_path = '/src/schema.json'
        data = read_json_file(schema_path)
        print(f"Schema file loaded successfully from {schema_path}")

        # Custom mapping rules
        custom_maps = [
            # Map echo endpoint to tool
            RouteMap(methods=["GET"],
                    pattern=r"^/v1/user/agents$",
                    route_type=RouteType.RESOURCE),
            # Map add endpoint to tool
            RouteMap(methods=["POST", "PUT", "DELETE"],
                    pattern=r"^/v1/user/agents$",
                    route_type=RouteType.TOOL)
        ]

        # Create a client for your API
        base_url = os.getenv('BASE_URL', 'https://api.redai.com')
        print(f"Using base URL: {base_url}")
        api_client = httpx.AsyncClient(base_url=base_url)

        print("Creating MCP server from OpenAPI spec...")
        mcp = FastMCP("RedAI-Simple-Server").from_openapi(
            openapi_spec=data,
            client=api_client,
            route_maps=custom_maps
        )

        print("MCP server created successfully!")
        print("Starting MCP server...")
        # Chỉ chạy một máy chủ MCP với transport "stdio" tại một thời điểm
        # Các máy chủ khác có thể được chạy với transport khác hoặc trong tiến trình riêng biệt

        # Nếu muốn chạy nhiều máy chủ MCP, bạn có thể sử dụng các cách sau:
        # 1. Sử dụng transport HTTP cho các máy chủ khác:
        #    mcp1.run(transport="http", host="localhost", port=3001)
        #    mcp2.run(transport="http", host="localhost", port=3002)
        # 2. Chạy các máy chủ trong các tiến trình riêng biệt

        # Hiện tại chỉ chạy một máy chủ với stdio để tránh lỗi UnicodeDecodeError
        mcp.run(transport="stdio")

        # Các máy chủ khác được comment lại để tránh lỗi
        # mcp1.run(transport="stdio")  # Lỗi: Không thể chạy nhiều máy chủ với stdio cùng lúc
        # mcp2.run(transport="stdio")  # Lỗi: Không thể chạy nhiều máy chủ với stdio cùng lúc
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
