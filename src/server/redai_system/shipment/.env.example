# Cấu hình cho Shipment MCP Server (GHN + GHTK)
# Sao chép file này thành .env và điền thông tin thực tế

# ============================================================================
# Cấu hình API GHN (Giao hàng Nhanh)
# ============================================================================

# Token xác thực GHN (bắt buộc)
# Lấy từ trang quản lý cửa hàng GHN
GHN_TOKEN=your_ghn_token_here

# ID cửa hàng GHN (bắt buộc)
# Lấy từ trang quản lý cửa hàng GHN
GHN_SHOP_ID=your_shop_id_here

# Môi trường API GHN (tùy chọn)
# Giá trị: "test" hoặc "production"
# Mặc định: "test"
GHN_ENVIRONMENT=test

# ============================================================================
# Cấu hình API GHTK (Giao hàng Tiết kiệm)
# ============================================================================

# Token xác thực GHTK (bắt buộc)
# Lấy từ trang quản lý đối tác GHTK
GHTK_TOKEN=your_ghtk_token_here

# Mã đối tác GHTK (bắt buộc)
# Mã cửa hàng hoặc mã đối tác do GHTK cung cấp
GHTK_PARTNER_CODE=your_partner_code_here

# Môi trường API GHTK (tùy chọn)
# Giá trị: "test" hoặc "production"
# Mặc định: "test"
GHTK_ENVIRONMENT=test

# ============================================================================
# Cấu hình API J&T Express
# ============================================================================

# Username J&T (bắt buộc)
# Tên đăng nhập tài khoản đối tác do J&T cấp trên Dashboard
JT_USERNAME=your_jt_username_here

# API Key J&T (bắt buộc)
# Khóa API được cấp, kiểm tra trên Dashboard J&T
JT_API_KEY=your_jt_api_key_here

# Customer Code J&T (bắt buộc)
# Mã khách hàng đối tác được cấp trên Dashboard J&T
JT_CUSTOMER_CODE=your_customer_code_here

# Môi trường API J&T (tùy chọn)
# Giá trị: "test" hoặc "production"
# Mặc định: "test"
JT_ENVIRONMENT=test

# ============================================================================
# Cấu hình API Ahamove
# ============================================================================

# API Key Ahamove (bắt buộc)
# Khóa API được cấp khi đăng ký đối tác Ahamove
AHAMOVE_API_KEY=your_ahamove_api_key_here

# Token Ahamove (bắt buộc)
# Token xác thực được lấy từ API authenticate
AHAMOVE_TOKEN=your_ahamove_token_here

# Mobile Ahamove (bắt buộc)
# Số điện thoại đăng ký tài khoản (format: 84xxxxxxxxx)
AHAMOVE_MOBILE=84912345678

# Môi trường API Ahamove (tùy chọn)
# Giá trị: "test" hoặc "production"
# Mặc định: "test"
AHAMOVE_ENVIRONMENT=test

# ============================================================================
# Cấu hình SSE Server (tùy chọn)
# ============================================================================

# Host cho SSE server (tùy chọn)
# Mặc định: "127.0.0.1"
GHN_SSE_HOST=127.0.0.1

# Port cho SSE server (tùy chọn)
# Mặc định: "8001"
GHN_SSE_PORT=8001

# ============================================================================
# Hướng dẫn lấy thông tin GHN
# ============================================================================

# 1. Đăng ký tài khoản GHN:
#    - Test: https://dev-online-gateway.ghn.vn
#    - Production: https://online-gateway.ghn.vn

# 2. Tạo cửa hàng và lấy thông tin:
#    - Đăng nhập vào trang quản lý
#    - Tạo cửa hàng mới
#    - Lấy Token và ShopID từ trang quản lý cửa hàng

# ============================================================================
# Hướng dẫn lấy thông tin GHTK
# ============================================================================

# 1. Đăng ký tài khoản GHTK:
#    - Website: https://ghtk.vn
#    - Đăng ký tài khoản đối tác

# 2. Lấy thông tin API:
#    - Đăng nhập vào trang quản lý đối tác
#    - Vào phần API/Tích hợp
#    - Lấy Token và Partner Code

# 3. Cấu hình webhook (tùy chọn):
#    - Cấu hình URL webhook để nhận thông báo cập nhật trạng thái
#    - URL webhook phải trả về HTTP 200

# ============================================================================
# Hướng dẫn lấy thông tin J&T Express
# ============================================================================

# 1. Đăng ký tài khoản J&T Express:
#    - Website: https://www.jet.co.id (Indonesia)
#    - Đăng ký tài khoản đối tác/merchant

# 2. Lấy thông tin API:
#    - Đăng nhập vào Dashboard J&T
#    - Vào phần API Management/Integration
#    - Lấy Username, API Key và Customer Code

# 3. Lưu ý quan trọng:
#    - J&T Express chủ yếu hoạt động tại Indonesia
#    - Số điện thoại phải có format +62xxxxxxxxxx
#    - API yêu cầu chữ ký bảo mật (MD5 + Base64)
#    - Sử dụng mã thành phố 3 ký tự viết hoa (JKT, BDG, SBY...)

# ============================================================================
# Hướng dẫn lấy thông tin Ahamove
# ============================================================================

# 1. Đăng ký tài khoản Ahamove:
#    - Website: https://ahamove.com
#    - Đăng ký tài khoản đối tác/doanh nghiệp

# 2. Lấy thông tin API:
#    - Liên hệ team Ahamove để được cấp API Key
#    - Sử dụng API authenticate để lấy token
#    - Token có thời hạn, cần refresh định kỳ

# 3. Tính năng đặc biệt:
#    - Hỗ trợ tài khoản cha-con (parent-child accounts)
#    - Nhiều loại dịch vụ: xe máy, xe ba gác, xe tải
#    - Giao hàng tức thì và đặt trước
#    - Webhook real-time cho cập nhật trạng thái
#    - Route optimization cho đơn nhiều điểm

# ============================================================================
# Cấu hình biến môi trường
# ============================================================================

# 1. Sao chép file này thành .env
# 2. Điền thông tin thực tế cho GHN, GHTK, J&T và/hoặc Ahamove
# 3. Chọn môi trường phù hợp (test/production)

# ============================================================================
# Ví dụ cấu hình
# ============================================================================

# Bỏ comment và điền thông tin thực tế:

# GHN Configuration
# GHN_TOKEN=abcd1234567890efgh
# GHN_SHOP_ID=12345
# GHN_ENVIRONMENT=test

# GHTK Configuration
# GHTK_TOKEN=xyz789012345abcd
# GHTK_PARTNER_CODE=PARTNER123
# GHTK_ENVIRONMENT=test

# J&T Configuration
# JT_USERNAME=merchant_username
# JT_API_KEY=jt_api_key_12345
# JT_CUSTOMER_CODE=CUSTOMER123
# JT_ENVIRONMENT=test

# Ahamove Configuration
# AHAMOVE_API_KEY=ahamove_api_key_12345
# AHAMOVE_TOKEN=ahamove_bearer_token_xyz
# AHAMOVE_MOBILE=84912345678
# AHAMOVE_ENVIRONMENT=test

# ============================================================================
# Cấu hình nhanh cho testing (không cần thay đổi)
# ============================================================================

# Nếu không cấu hình, server sẽ tự động sử dụng giá trị test
# Server vẫn có thể chạy được nhưng API calls sẽ fail
# Để test đầy đủ, hãy cấu hình thông tin thực tế cho từng API cần sử dụng:
#   - GHN: Hoạt động tại Việt Nam
#   - GHTK: Hoạt động tại Việt Nam
#   - J&T: Hoạt động chủ yếu tại Indonesia
#   - Ahamove: Hoạt động tại Việt Nam, Thái Lan, Singapore
