# RedAI Data Module Server Configuration
# Copy file này thành .env và cập nhật các giá trị

# API Configuration
REDAI_DATA_API_BASE_URL=https://api.redai.com

# Server Configuration
DATA_MODULE_HTTP_HOST=127.0.0.1
DATA_MODULE_HTTP_PORT=8002
DATA_MODULE_HTTP_PATH=/mcp

# Authentication Configuration (chọn một trong hai phương pháp)

# Phương pháp 1: Sử dụng Public Key (cho development/testing)
# DATA_MODULE_AUTH_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----"

# Phương pháp 2: Sử dụng JWKS URI (k<PERSON><PERSON>ến nghị cho production)
# DATA_MODULE_AUTH_JWKS_URI=https://your-auth-provider.com/.well-known/jwks.json

# Optional: JWT Claims Validation
# DATA_MODULE_AUTH_ISSUER=https://your-auth-provider.com
# DATA_MODULE_AUTH_AUDIENCE=redai-data-module

# Transport Configuration
DATA_MODULE_TRANSPORT=streamable-http
