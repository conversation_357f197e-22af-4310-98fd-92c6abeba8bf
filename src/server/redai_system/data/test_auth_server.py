#!/usr/bin/env python3
"""
Test script cho RedAI Data Module MCP Server với Bearer Token Authentication

Script này test:
1. Tạo key pair và token cho development
2. Khởi động server với authentication
3. Connect từ client với Bearer token
4. Test các tools với authentication
"""

import asyncio
import os
import sys
from pathlib import Path

# Thêm path để import modules
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport
from fastmcp.server.auth.providers.bearer import RSAKeyPair

async def test_auth_flow():
    """
    Test authentication flow với Bearer token
    """
    print("🧪 Testing RedAI Data Module Server Authentication")
    print("=" * 60)
    
    # 1. Tạo key pair và token cho testing
    print("1️⃣ Tạo key pair và token...")
    key_pair = RSAKeyPair.generate()
    
    # Tạo token với thông tin test
    token = key_pair.create_token(
        subject="test-user-123",
        issuer="https://dev.redai.com",
        audience="redai-data-module",
        scopes=["read", "write", "admin"],
        expires_in_seconds=3600  # 1 giờ
    )
    
    print(f"✅ Key pair created")
    print(f"📝 Test token: {token[:50]}...")
    print()
    
    # 2. Cấu hình environment cho server
    print("2️⃣ Cấu hình environment variables...")
    os.environ["DATA_MODULE_AUTH_PUBLIC_KEY"] = key_pair.public_key
    os.environ["DATA_MODULE_AUTH_ISSUER"] = "https://dev.redai.com"
    os.environ["DATA_MODULE_AUTH_AUDIENCE"] = "redai-data-module"
    os.environ["REDAI_DATA_API_BASE_URL"] = "https://api.redai.com"
    
    print("✅ Environment configured")
    print()
    
    # 3. Import và tạo server (sau khi set environment)
    print("3️⃣ Khởi tạo server...")
    try:
        from data_module_server import DataModuleServer
        
        server_instance = DataModuleServer()
        mcp_server = server_instance.create_server()
        
        print("✅ Server created successfully")
        print(f"📊 Server name: {mcp_server.name}")
        print()
        
    except Exception as e:
        print(f"❌ Lỗi khi tạo server: {e}")
        return False
    
    # 4. Test client connection với Bearer token
    print("4️⃣ Test client connection...")
    try:
        # Tạo transport với Bearer token
        transport = StreamableHttpTransport(
            url="http://127.0.0.1:8002/mcp",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        print("✅ Transport created with Bearer token")
        print(f"🔑 Token: {token[:30]}...")
        print()
        
        # Note: Trong test này chúng ta chỉ test việc tạo transport
        # Để test connection thực tế, cần chạy server trước
        print("💡 Để test connection thực tế:")
        print("   1. Chạy server: python data_module_server.py")
        print("   2. Chạy client test với token đã tạo")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi test client: {e}")
        return False

async def test_client_with_server():
    """
    Test client connection với server đang chạy
    (Cần chạy server trước)
    """
    print("🔗 Testing client connection to running server...")
    
    # Tạo token test
    key_pair = RSAKeyPair.generate()
    token = key_pair.create_token(
        subject="test-user-456",
        issuer="https://dev.redai.com", 
        audience="redai-data-module",
        scopes=["read", "write"]
    )
    
    try:
        transport = StreamableHttpTransport(
            url="http://127.0.0.1:8002/mcp",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        async with Client(transport) as client:
            print("✅ Connected to server successfully")
            
            # Test check_auth_status tool
            result = await client.call_tool("check_auth_status")
            print(f"🔍 Auth status: {result}")
            
            # List available tools
            tools = await client.list_tools()
            print(f"🔧 Available tools: {len(tools)} tools")
            for tool in tools[:5]:  # Show first 5 tools
                print(f"   • {tool.name}: {tool.description[:50]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ Lỗi khi connect: {e}")
        print("💡 Đảm bảo server đang chạy: python data_module_server.py")
        return False

def print_usage_instructions():
    """
    In hướng dẫn sử dụng
    """
    print("📋 Hướng dẫn sử dụng:")
    print("=" * 40)
    print("1. Test authentication setup:")
    print("   python test_auth_server.py")
    print()
    print("2. Chạy server với authentication:")
    print("   python data_module_server.py")
    print()
    print("3. Test client connection (server phải đang chạy):")
    print("   python test_auth_server.py --test-client")
    print()

async def main():
    """
    Main function
    """
    if len(sys.argv) > 1 and sys.argv[1] == "--test-client":
        # Test client connection
        success = await test_client_with_server()
    else:
        # Test authentication setup
        success = await test_auth_flow()
    
    print()
    if success:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")
    
    print()
    print_usage_instructions()

if __name__ == "__main__":
    asyncio.run(main())
