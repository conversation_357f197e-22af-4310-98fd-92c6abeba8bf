# RedAI Data Module MCP Server

Server MCP cho RedAI Data Module API sử dụng FastMCP và OpenAPI Schema với Bearer Token Authentication.

## Tính năng

- ✅ Tự động tạo tools từ OpenAPI specification
- 🔐 Bearer token authentication từ client khi connect
- 🚀 HTTP transport với FastMCP
- 🔄 Tự động sử dụng access token cho tất cả API calls
- 📊 Xử lý parameters, headers và request body tự động

## Cấu hình Authentication

### Phương pháp 1: Public Key (Development/Testing)

```bash
# Trong file .env
DATA_MODULE_AUTH_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----"
DATA_MODULE_AUTH_ISSUER=https://dev.redai.com
DATA_MODULE_AUTH_AUDIENCE=redai-data-module
```

### Phương pháp 2: JWKS URI (Production)

```bash
# Trong file .env
DATA_MODULE_AUTH_JWKS_URI=https://auth.redai.com/.well-known/jwks.json
DATA_MODULE_AUTH_ISSUER=https://auth.redai.com
DATA_MODULE_AUTH_AUDIENCE=redai-data-module
```

## Cách sử dụng

### 1. Khởi động Server

```bash
cd src/server/redai_system/data
python data_module_server.py
```

### 2. Connect từ Client với Bearer Token

Client cần gửi Bearer token trong Authorization header khi connect:

```python
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

# Tạo transport với Bearer token
transport = StreamableHttpTransport(
    url="http://127.0.0.1:8002/mcp",
    headers={"Authorization": "Bearer your-access-token-here"}
)

# Connect và sử dụng
async with Client(transport) as client:
    # Token sẽ được tự động sử dụng cho tất cả API calls
    result = await client.call_tool("check_auth_status")
    print(result)
```

### 3. Sử dụng Tools

Tất cả tools được tạo tự động từ OpenAPI spec và sẽ tự động sử dụng Bearer token từ client:

```python
# Kiểm tra authentication status
result = await client.call_tool("check_auth_status")

# Gọi API endpoints (ví dụ)
result = await client.call_tool("get_user_media", user_id="123")
result = await client.call_tool("create_url_resource", data={"url": "https://example.com"})
```

## Cấu trúc API

- **Media endpoints**: Quản lý media files của user
- **URL endpoints**: Quản lý URL resources của user  
- **Statistics endpoints**: Thống kê dữ liệu của user

## Authentication Flow

1. Client connect với Bearer token trong Authorization header
2. Server validate token bằng public key hoặc JWKS
3. Token được lưu trong context và tự động sử dụng cho mọi API call
4. Không cần tool để update token - được xử lý tự động

## Lỗi thường gặp

### "Không thể xác thực"
- Đảm bảo Bearer token được gửi trong Authorization header khi connect
- Kiểm tra token còn hạn và đúng format JWT
- Xác nhận cấu hình authentication (public key hoặc JWKS URI)

### "API call failed"
- Token có thể đã hết hạn
- Kiểm tra API base URL trong cấu hình
- Đảm bảo token có đủ quyền cho endpoint được gọi

## Development

Để test server trong môi trường development, bạn có thể sử dụng RSAKeyPair utility để tạo token:

```python
from fastmcp.server.auth.providers.bearer import RSAKeyPair

# Tạo key pair
key_pair = RSAKeyPair.generate()

# Tạo token
token = key_pair.create_token(
    subject="test-user",
    issuer="https://dev.redai.com",
    audience="redai-data-module",
    scopes=["read", "write"]
)

print(f"Public Key: {key_pair.public_key}")
print(f"Test Token: {token}")
```

## Environment Variables

```bash
# API Configuration
REDAI_DATA_API_BASE_URL=https://api.redai.com

# Server Configuration  
DATA_MODULE_HTTP_HOST=127.0.0.1
DATA_MODULE_HTTP_PORT=8002
DATA_MODULE_HTTP_PATH=/mcp

# Authentication (chọn một phương pháp)
DATA_MODULE_AUTH_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----..."
# hoặc
DATA_MODULE_AUTH_JWKS_URI=https://auth.redai.com/.well-known/jwks.json

# Optional JWT validation
DATA_MODULE_AUTH_ISSUER=https://auth.redai.com
DATA_MODULE_AUTH_AUDIENCE=redai-data-module

# Transport
DATA_MODULE_TRANSPORT=streamable-http
```
