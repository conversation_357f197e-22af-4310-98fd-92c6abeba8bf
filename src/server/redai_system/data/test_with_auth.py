#!/usr/bin/env python3
"""
Test script để test server với authentication thực tế
"""

import asyncio
import os
import sys
from pathlib import Path

# Thêm path để import modules
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport
from fastmcp.server.auth.providers.bearer import RSAKeyPair

async def test_with_auth():
    """
    Test server với authentication
    """
    print("🧪 Testing RedAI Data Module Server với Authentication")
    print("=" * 60)
    
    # 1. Tạo key pair và token
    print("1️⃣ Tạo key pair và token...")
    key_pair = RSAKeyPair.generate()
    
    token = key_pair.create_token(
        subject="test-user-123",
        issuer="https://dev.redai.com",
        audience="redai-data-module",
        scopes=["read", "write", "admin"],
        expires_in_seconds=3600
    )
    
    print(f"✅ Token created: {token[:50]}...")
    print(f"📋 Public Key (để cấu hình server):")
    print(f"DATA_MODULE_AUTH_PUBLIC_KEY=\"{key_pair.public_key}\"")
    print(f"DATA_MODULE_AUTH_ISSUER=https://dev.redai.com")
    print(f"DATA_MODULE_AUTH_AUDIENCE=redai-data-module")
    print()
    
    # 2. Test connection
    print("2️⃣ Test connection với server...")
    try:
        transport = StreamableHttpTransport(
            url="http://127.0.0.1:8002/mcp",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        async with Client(transport) as client:
            print("✅ Connected to server successfully!")
            
            # Test check_auth_status tool
            print("\n3️⃣ Test check_auth_status tool...")
            try:
                result = await client.call_tool("check_auth_status")
                print(f"🔍 Auth status result:")
                print(result)
            except Exception as e:
                print(f"❌ Error calling check_auth_status: {e}")
            
            # List available tools
            print("\n4️⃣ List available tools...")
            try:
                tools = await client.list_tools()
                print(f"🔧 Found {len(tools)} tools:")
                for i, tool in enumerate(tools[:10], 1):  # Show first 10 tools
                    print(f"   {i}. {tool.name}")
                    if tool.description:
                        print(f"      📝 {tool.description[:80]}...")
                if len(tools) > 10:
                    print(f"   ... and {len(tools) - 10} more tools")
            except Exception as e:
                print(f"❌ Error listing tools: {e}")
            
            return True
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\n💡 Đảm bảo:")
        print("   1. Server đang chạy: python data_module_server.py")
        print("   2. Server được cấu hình với public key ở trên")
        print("   3. Server đang listen trên http://127.0.0.1:8002/mcp")
        return False

async def main():
    """
    Main function
    """
    success = await test_with_auth()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Test hoàn thành thành công!")
        print("🎉 Server authentication đang hoạt động đúng!")
    else:
        print("❌ Test thất bại!")
        print("🔧 Kiểm tra lại cấu hình server và thử lại")

if __name__ == "__main__":
    asyncio.run(main())
