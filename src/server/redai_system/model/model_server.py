"""
Server MCP cho RedAI Model Module API sử dụng FastMCP và OpenAPI Schema

Server này tự động tạo các MCP tools và resources từ model/swagger.json,
cung cấp giao diện MCP cho tất cả các endpoint của Model Module API.

Tính năng:
- Tự động tạo tools từ OpenAPI specification
- Hỗ trợ Bearer token authentication từ client
- Streamable HTTP transport với FastMCP
- Tùy chỉnh route mapping cho các loại endpoint khác nhau
- Xử lý parameters, headers và request body tự động

Cấu trúc API:
- Key LLM endpoints: Quản lý API keys LLM với model auto-discovery
- Models endpoints: Quản lý user models, system models, fine-tune models
- Data Fine Tune endpoints: Quản lý datasets fine-tuning với upload
- Fine Tuning Jobs endpoints: Tạo và quản lý fine-tuning jobs

Authentication:
- Bearer token được truyền từ client khi kết nối
- Tự động xử lý authentication cho tất cả requests
"""

import os
import json
import httpx
from pathlib import Path
from typing import Optional, Dict, Any

# FastMCP imports
from fastmcp import FastMCP, Context

# Cấu hình môi trường (không bao gồm BEARER_TOKEN - sẽ nhận từ client)
API_BASE_URL = os.getenv("REDAI_MODEL_API_BASE_URL", "https://api.redai.com")
HTTP_HOST = os.getenv("MODEL_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("MODEL_HTTP_PORT", "8006"))
HTTP_PATH = os.getenv("MODEL_HTTP_PATH", "/mcp")

def load_openapi_schema() -> dict:
    """
    Tải OpenAPI schema từ file swagger.json trong cùng thư mục
    """
    schema_path = Path(__file__).parent / "swagger.json"
    if not schema_path.exists():
        raise FileNotFoundError(f"Không tìm thấy file schema tại: {schema_path}")

    with open(schema_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_authenticated_client(base_url: str, bearer_token: Optional[str] = None) -> httpx.AsyncClient:
    """
    Tạo HTTP client với Bearer token authentication
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if bearer_token:
        headers["Authorization"] = f"Bearer {bearer_token}"
    
    return httpx.AsyncClient(
        base_url=base_url,
        headers=headers,
        timeout=30.0
    )

class ModelServer:
    """
    Class để quản lý Model MCP Server với authentication động
    """
    
    def __init__(self):
        self.openapi_spec = load_openapi_schema()
        self.base_url = API_BASE_URL
        self._mcp_server = None
        
    def create_server_with_auth(self, bearer_token: Optional[str] = None) -> FastMCP:
        """
        Tạo MCP server với Bearer token cụ thể
        """
        # Tạo authenticated HTTP client
        api_client = create_authenticated_client(self.base_url, bearer_token)
        
        # Tạo MCP server từ OpenAPI spec
        try:
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
                name="RedAI-Model-Server",
                timeout=30.0,
            )
        except TypeError:
            # Fallback cho phiên bản cũ hơn của FastMCP
            mcp = FastMCP.from_openapi(
                openapi_spec=self.openapi_spec,
                client=api_client,
            )
        
        # Tùy chỉnh components
        self._customize_mcp_components(mcp)
        
        # Thêm tool để cập nhật authentication
        self._add_auth_tools(mcp)
        
        # Thêm các tools tùy chỉnh cho model
        self._add_model_tools(mcp)
        
        return mcp
    
    def _customize_mcp_components(self, mcp_server: FastMCP) -> None:
        """
        Tùy chỉnh MCP components sau khi được tạo từ OpenAPI spec
        """
        # Tạm thời bỏ qua customization để tránh lỗi với FastMCP structure
        # Có thể implement sau khi hiểu rõ hơn về FastMCP API
        pass
    
    def _add_auth_tools(self, mcp_server: FastMCP) -> None:
        """
        Thêm các tools để quản lý authentication
        """
        
        @mcp_server.tool()
        async def update_bearer_token(bearer_token: str, ctx: Context) -> str:
            """
            Cập nhật Bearer token cho các API calls tiếp theo
            
            Args:
                bearer_token: Bearer token mới để xác thực
                
            Returns:
                Thông báo xác nhận việc cập nhật token
            """
            try:
                # Tạo client mới với token mới
                new_client = create_authenticated_client(self.base_url, bearer_token)
                
                await ctx.info(f"🔑 Đã cập nhật Bearer token thành công")
                
                return f"✅ Bearer token đã được cập nhật thành công. Các API calls tiếp theo sẽ sử dụng token mới."
                
            except Exception as e:
                await ctx.error(f"❌ Lỗi khi cập nhật Bearer token: {str(e)}")
                return f"❌ Không thể cập nhật Bearer token: {str(e)}"
        
        @mcp_server.tool()
        async def check_auth_status(ctx: Context) -> str:
            """
            Kiểm tra trạng thái authentication hiện tại
            
            Returns:
                Thông tin về trạng thái authentication
            """
            try:
                # Thử gọi một endpoint đơn giản để kiểm tra auth
                test_client = create_authenticated_client(self.base_url, None)
                
                # Kiểm tra xem có Authorization header không
                has_auth = "Authorization" in test_client.headers
                
                if has_auth:
                    await ctx.info("🔑 Authentication đã được cấu hình")
                    return "✅ Authentication đã được cấu hình và sẵn sàng sử dụng"
                else:
                    await ctx.info("⚠️ Chưa có authentication")
                    return "⚠️ Chưa có Bearer token. Sử dụng tool 'update_bearer_token' để cấu hình authentication"
                    
            except Exception as e:
                await ctx.error(f"❌ Lỗi khi kiểm tra auth status: {str(e)}")
                return f"❌ Không thể kiểm tra trạng thái authentication: {str(e)}"

    def _add_model_tools(self, mcp_server: FastMCP) -> None:
        """
        Thêm các tools tùy chỉnh cho model functionality
        """
        
        @mcp_server.tool()
        async def get_model_summary(ctx: Context) -> str:
            """
            Lấy tổng quan về models của người dùng
            
            Returns:
                Thông tin tổng quan về API keys, models, datasets và fine-tuning jobs
            """
            try:
                await ctx.info("🤖 Đang tải thông tin tổng quan models...")
                
                summary = {
                    "api_keys": "Sử dụng tools để quản lý API keys LLM với auto-discovery",
                    "user_models": "Sử dụng tools để xem models từ API keys của bạn", 
                    "system_models": "Sử dụng tools để xem models hệ thống có sẵn",
                    "fine_tune_models": "Sử dụng tools để xem models đã fine-tune",
                    "datasets": "Sử dụng tools để quản lý datasets fine-tuning",
                    "fine_tuning_jobs": "Sử dụng tools để tạo và quản lý fine-tuning jobs"
                }
                
                return f"🤖 Model Summary:\n" + "\n".join([f"• {k}: {v}" for k, v in summary.items()])
                
            except Exception as e:
                await ctx.error(f"❌ Lỗi khi lấy model summary: {str(e)}")
                return f"❌ Không thể lấy thông tin tổng quan: {str(e)}"

        @mcp_server.tool()
        async def calculate_fine_tune_cost(training_tokens: int, provider: str = "OPENAI") -> str:
            """
            Tính toán chi phí fine-tuning dự kiến
            
            Args:
                training_tokens: Số lượng tokens training
                provider: Nhà cung cấp (OPENAI, GOOGLE, etc.)
                
            Returns:
                Thông tin chi tiết về chi phí fine-tuning
            """
            try:
                # Giá cơ bản cho fine-tuning (USD per 1K tokens)
                pricing = {
                    "OPENAI": {
                        "gpt-3.5-turbo": 0.008,
                        "gpt-4": 0.03,
                        "base_rate": 0.008
                    },
                    "GOOGLE": {
                        "base_rate": 0.002
                    },
                    "ANTHROPIC": {
                        "base_rate": 0.01
                    }
                }
                
                base_rate = pricing.get(provider, {}).get("base_rate", 0.01)
                cost_usd = (training_tokens / 1000) * base_rate
                cost_vnd = cost_usd * 24000  # Tỷ giá USD/VND ước tính
                
                result = {
                    "provider": provider,
                    "training_tokens": f"{training_tokens:,}",
                    "rate_per_1k_tokens": f"${base_rate}",
                    "estimated_cost_usd": f"${cost_usd:.4f}",
                    "estimated_cost_vnd": f"{cost_vnd:,.0f} VND",
                    "note": "Chi phí ước tính, có thể thay đổi theo thực tế"
                }
                
                return f"💰 Fine-tuning Cost Estimation:\n" + "\n".join([f"• {k}: {v}" for k, v in result.items()])
                
            except Exception as e:
                return f"❌ Không thể tính toán chi phí: {str(e)}"

        @mcp_server.tool()
        async def suggest_model_for_task(task_type: str, budget: str = "medium") -> str:
            """
            Gợi ý model phù hợp cho loại task cụ thể
            
            Args:
                task_type: Loại task (text_generation, code_generation, chat, analysis, etc.)
                budget: Ngân sách (low, medium, high)
                
            Returns:
                Danh sách models được gợi ý
            """
            try:
                # Gợi ý models dựa trên task và budget
                suggestions = {
                    "text_generation": {
                        "low": ["gpt-3.5-turbo", "claude-3-haiku"],
                        "medium": ["gpt-4", "claude-3-sonnet"],
                        "high": ["gpt-4-turbo", "claude-3-opus"]
                    },
                    "code_generation": {
                        "low": ["gpt-3.5-turbo", "codellama-7b"],
                        "medium": ["gpt-4", "claude-3-sonnet"],
                        "high": ["gpt-4-turbo", "claude-3-opus"]
                    },
                    "chat": {
                        "low": ["gpt-3.5-turbo", "claude-3-haiku"],
                        "medium": ["gpt-4", "claude-3-sonnet"],
                        "high": ["gpt-4-turbo", "claude-3-opus"]
                    },
                    "analysis": {
                        "low": ["gpt-3.5-turbo"],
                        "medium": ["gpt-4", "claude-3-sonnet"],
                        "high": ["gpt-4-turbo", "claude-3-opus"]
                    }
                }
                
                task_suggestions = suggestions.get(task_type, suggestions["text_generation"])
                models = task_suggestions.get(budget, task_suggestions["medium"])
                
                result = {
                    "task_type": task_type,
                    "budget": budget,
                    "recommended_models": models,
                    "note": "Gợi ý dựa trên hiệu suất và chi phí tổng quát"
                }
                
                return f"🎯 Model Recommendations:\n" + "\n".join([f"• {k}: {v}" for k, v in result.items()])
                
            except Exception as e:
                return f"❌ Không thể gợi ý models: {str(e)}"

        @mcp_server.tool()
        async def validate_api_key_format(api_key: str, provider: str) -> str:
            """
            Kiểm tra format của API key trước khi tạo
            
            Args:
                api_key: API key cần kiểm tra
                provider: Nhà cung cấp (OPENAI, ANTHROPIC, etc.)
                
            Returns:
                Kết quả validation
            """
            try:
                # Patterns cho các providers
                patterns = {
                    "OPENAI": {"prefix": "sk-", "min_length": 40},
                    "ANTHROPIC": {"prefix": "sk-ant-", "min_length": 40},
                    "GOOGLE": {"prefix": "", "min_length": 20},
                    "AZURE": {"prefix": "", "min_length": 20},
                    "COHERE": {"prefix": "", "min_length": 20},
                    "MISTRAL": {"prefix": "", "min_length": 20},
                    "PERPLEXITY": {"prefix": "pplx-", "min_length": 30},
                    "GROQ": {"prefix": "gsk_", "min_length": 30}
                }
                
                pattern = patterns.get(provider, {"prefix": "", "min_length": 20})
                
                issues = []
                if pattern["prefix"] and not api_key.startswith(pattern["prefix"]):
                    issues.append(f"API key phải bắt đầu với '{pattern['prefix']}'")
                
                if len(api_key) < pattern["min_length"]:
                    issues.append(f"API key phải có ít nhất {pattern['min_length']} ký tự")
                
                if not api_key.strip():
                    issues.append("API key không được để trống")
                
                if issues:
                    return f"❌ API Key Validation Failed:\n" + "\n".join([f"• {issue}" for issue in issues])
                else:
                    return f"✅ API Key format hợp lệ cho {provider}"
                
            except Exception as e:
                return f"❌ Không thể validate API key: {str(e)}"

# Tạo instance của ModelServer
model_server = ModelServer()

# Tạo MCP server mặc định (không có authentication)
mcp = model_server.create_server_with_auth()

def create_server_with_token(bearer_token: str) -> FastMCP:
    """
    Tạo server với Bearer token cụ thể (để sử dụng từ client)
    """
    return model_server.create_server_with_auth(bearer_token)

def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(f"🚀 Khởi động server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path="/mcp"
            )

        elif transport == "sse":
            print(f"🚀 Khởi động server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise

def print_server_info():
    """
    In thông tin về server và các endpoints có sẵn
    """
    try:
        schema = model_server.openapi_spec
        endpoints = list(schema.get("paths", {}).keys())

        print("="*70)
        print("🤖 RedAI Model MCP Server")
        print("="*70)
        print("📋 Cấu hình:")
        print(f"   🌐 API Base URL: {API_BASE_URL}")
        print(f"   🔑 Authentication: Nhận từ client khi kết nối")
        print(f"   🚀 Transport: Streamable HTTP (FastMCP)")
        print(f"   🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"   📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}/mcp")
        print()

        print(f"📊 API Information:")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   📝 Description: {schema.get('info', {}).get('description', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(endpoints)} endpoints")
        print()

        print("📋 Available Endpoints:")
        for path in endpoints:
            methods = list(schema["paths"][path].keys())
            # Lấy tags từ operation đầu tiên
            first_method = list(schema["paths"][path].values())[0]
            tags = first_method.get("tags", [])
            tag_str = f" [{', '.join(tags)}]" if tags else ""
            print(f"   • {path} [{', '.join(method.upper() for method in methods)}]{tag_str}")

        print()
        print("🔧 Authentication Tools:")
        print("   • update_bearer_token: Cập nhật Bearer token")
        print("   • check_auth_status: Kiểm tra trạng thái authentication")
        print("   • get_model_summary: Lấy tổng quan models")
        print("   • calculate_fine_tune_cost: Tính toán chi phí fine-tuning")
        print("   • suggest_model_for_task: Gợi ý model cho task")
        print("   • validate_api_key_format: Kiểm tra format API key")

        print()
        print("🚀 Transport Options:")
        print("   • Streamable HTTP: http://127.0.0.1:8006/mcp (mặc định)")
        print("   • SSE: http://127.0.0.1:8006/sse")
        print("   • STDIO: Command line interface")
        print("   💡 Sử dụng: python model_server.py [streamable-http|sse|stdio]")

        print("="*70)

    except Exception as e:
        print(f"❌ Lỗi khi tải thông tin server: {str(e)}")

def main():
    """
    Hàm main để khởi chạy MCP server
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')

        # In thông tin server
        print_server_info()
        print("🚀 Đang khởi động server...")
        print("💡 Sử dụng tool 'update_bearer_token' để cấu hình authentication từ client")
        print()

        # Lấy transport từ environment variable hoặc command line args
        preferred_transport = os.getenv("MODEL_TRANSPORT", "streamable-http")

        # Kiểm tra command line arguments
        import sys
        if len(sys.argv) > 1:
            if sys.argv[1] in ["streamable-http", "http", "sse", "stdio"]:
                preferred_transport = sys.argv[1]

        print(f"🔄 Sử dụng {preferred_transport} transport...")

        # Thử khởi động với transport được chọn
        try:
            run_server_with_transport(preferred_transport)
        except Exception as e:
            print(f"❌ Lỗi với {preferred_transport} transport: {str(e)}")

            # Fallback sang streamable-http nếu không phải streamable-http
            if preferred_transport != "streamable-http":
                print("🔄 Thử fallback sang streamable-http transport...")
                try:
                    run_server_with_transport("streamable-http")
                except Exception as e2:
                    print(f"❌ Lỗi với streamable-http transport: {str(e2)}")
                    raise e2
            else:
                raise e

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
