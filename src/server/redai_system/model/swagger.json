{"openapi": "3.0.0", "info": {"title": "Models Module API", "description": "API documentation for Models Module - <PERSON><PERSON><PERSON>n lý hệ thống models AI cho người dùng bao gồm quản lý API keys, models, fine-tuning và datasets", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User - Key LLM", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> keys LLM cho người dùng"}, {"name": "User - Models", "description": "<PERSON><PERSON><PERSON><PERSON> lý models cho ng<PERSON><PERSON>i dùng"}, {"name": "User - Data Fine Tune", "description": "<PERSON><PERSON><PERSON><PERSON> lý datasets fine-tuning cho người dùng"}, {"name": "User - <PERSON> Tuning Jobs", "description": "<PERSON><PERSON><PERSON><PERSON> lý fine-tuning jobs cho người dùng"}], "paths": {"/user/key-llm": {"post": {"operationId": "createUserKeyLlm", "tags": ["User - Key LLM"], "summary": "Tạo mới user key LLM với model auto-discovery", "description": "Tự động discovery models từ provider sau khi tạo key thành công", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserKeyLlmDto"}}}}, "responses": {"201": {"description": "Tạo mới user key LLM thành công với thông tin model discovery", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "User key LLM created successfully"}, "result": {"$ref": "#/components/schemas/CreateUserKeyLlmResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "get": {"operationId": "getUserKeyLlmList", "tags": ["User - Key LLM"], "summary": "<PERSON><PERSON><PERSON> s<PERSON>ch user key LLM có phân trang", "description": "API này hỗ trợ tìm kiếm theo tên key, phân trang và sắp xếp", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tên key", "required": false, "schema": {"type": "string", "example": "My OpenAI Key"}}, {"name": "provider", "in": "query", "description": "<PERSON><PERSON><PERSON> theo nhà cung cấp", "required": false, "schema": {"type": "string", "enum": ["OPENAI", "ANTHROPIC", "GOOGLE", "AZURE", "COHERE", "MISTRAL", "PERPLEXITY", "GROQ"], "example": "OPENAI"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["name", "provider", "createdAt", "updatedAt"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch user key LLM", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedUserKeyLlmResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/key-llm/{id}": {"patch": {"operationId": "updateUserKeyLlm", "tags": ["User - Key LLM"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t user key LLM với model auto-discovery", "description": "<PERSON><PERSON><PERSON> cập nhật API key, sẽ tự động discovery models mới từ provider", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của user key LLM", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserKeyLlmDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật user key LLM thành công với thông tin model discovery", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "User key LLM updated successfully"}, "result": {"$ref": "#/components/schemas/UpdateUserKeyLlmResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"operationId": "deleteUserKeyLlm", "tags": ["User - Key LLM"], "summary": "Xóa user key LLM và clear model mappings", "description": "Xóa user key LLM và tất cả các model mappings liên quan", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của user key LLM", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Xóa user key LLM thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "User key LLM deleted successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/key-llm/{id}/reload-models": {"post": {"operationId": "reloadUserKeyLlmModels", "tags": ["User - Key LLM"], "summary": "Reload models từ user key LLM", "description": "<PERSON><PERSON><PERSON><PERSON> hiện lại discovery models từ provider cho key cụ thể", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của user key LLM", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Reload models thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Models reloaded successfully"}, "result": {"$ref": "#/components/schemas/ReloadModelsResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/models/user-models-by-keys/{keyllmId}": {"get": {"operationId": "getUserModelsByKey", "tags": ["User - Models"], "summary": "<PERSON><PERSON><PERSON> s<PERSON> models theo user keys", "description": "<PERSON><PERSON><PERSON> s<PERSON>ch models mà user có thể sử dụng thông qua LLM key cụ thể", "security": [{"bearerAuth": []}], "parameters": [{"name": "keyllmId", "in": "path", "description": "ID của user LLM key", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo model ID", "required": false, "schema": {"type": "string", "example": "gpt-4"}}], "responses": {"200": {"description": "<PERSON><PERSON> models theo user keys", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedUserModelsResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/models/system-models": {"get": {"operationId": "getSystemModels", "tags": ["User - Models"], "summary": "<PERSON><PERSON><PERSON> danh sách active system models", "description": "<PERSON><PERSON><PERSON> s<PERSON>ch models hệ thống đang hoạt động mà user có thể sử dụng", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo model ID", "required": false, "schema": {"type": "string", "example": "gpt-4"}}, {"name": "provider", "in": "query", "description": "<PERSON><PERSON><PERSON> theo n<PERSON> cung cấp (bắt buộc)", "required": true, "schema": {"type": "string", "enum": ["OPENAI", "ANTHROPIC", "GOOGLE", "AZURE", "COHERE", "MISTRAL", "PERPLEXITY", "GROQ"], "example": "OPENAI"}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON> active system models", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedSystemModelsResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/models/fine-tune-datasets": {"get": {"operationId": "getFineTuneDatasets", "tags": ["User - Models"], "summary": "<PERSON><PERSON><PERSON> s<PERSON>ch user model fine-tune của user", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch các model đã fine-tune mà user đã tạo", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON> khóa tìm kiếm theo tên model", "required": false, "schema": {"type": "string", "example": "my-custom-model"}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON> user model fine-tune của user", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedUserModelFineTuneResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/data-fine-tune": {"get": {"operationId": "getDataFineTuneList", "tags": ["User - Data Fine Tune"], "summary": "<PERSON><PERSON><PERSON> danh sách dataset fine tune của user có phân trang", "description": "API này hỗ trợ tìm kiếm theo tên dataset, phân trang và sắp xếp", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tên dataset", "required": false, "schema": {"type": "string", "example": "Customer Service Dataset"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái dataset", "required": false, "schema": {"type": "string", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"], "example": "COMPLETED"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["createdAt", "name"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON>h s<PERSON>ch dataset fine tune", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedUserDataFineTuneResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/data-fine-tune/upload-url": {"get": {"operationId": "getDataFineTuneUploadUrl", "tags": ["User - Data Fine Tune"], "summary": "Lấy URL upload dataset", "description": "Lấy URL để upload file dataset lên cloud storage", "security": [{"bearerAuth": []}], "parameters": [{"name": "mime", "in": "query", "description": "MIME type của file cần upload", "required": true, "schema": {"type": "string", "example": "application/json"}}], "responses": {"200": {"description": "URL upload dataset", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"type": "object", "properties": {"uploadUrl": {"type": "string", "example": "https://s3.amazonaws.com/bucket/upload-url"}, "viewUrl": {"type": "string", "example": "https://cdn.example.com/view-url", "nullable": true}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/data-fine-tune/{id}": {"get": {"operationId": "getDataFineTuneDetail", "tags": ["User - Data Fine Tune"], "summary": "<PERSON><PERSON><PERSON> chi tiết dataset fine tune", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của dataset fine tune theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của dataset fine tune", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "<PERSON> tiết dataset fine tune", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/UserDataFineTuneDetailResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "patch": {"tags": ["User - Data Fine Tune"], "summary": "<PERSON>ậ<PERSON> nhật dataset fine tune", "description": "<PERSON><PERSON><PERSON> nhật thông tin dataset fine tune", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của dataset fine tune", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDataFineTuneDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật dataset fine tune thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Dataset updated successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Data Fine Tune"], "summary": "Xóa dataset fine tune", "description": "Xóa dataset fine tune và các file liên quan", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của dataset fine tune", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Xóa dataset fine tune thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Dataset deleted successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/data-fine-tune/{id}/upload-url-success": {"patch": {"operationId": "updateDataFineTuneUploadSuccess", "tags": ["User - Data Fine Tune"], "summary": "<PERSON><PERSON><PERSON> nhật trạng thái upload dataset", "description": "Cập nhật trạng thái dataset sau khi upload file thành công", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của dataset fine tune", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật trạng thái dataset thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Dataset status updated successfully"}, "result": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/fine-tuning-jobs": {"post": {"operationId": "createFineTuningJob", "tags": ["User - <PERSON> Tuning Jobs"], "summary": "Tạo fine-tuning job mới", "description": "Tạo fine-tuning job mới cho OpenAI hoặc Google AI. Quy trình: Validate dataset và model cơ sở → Tính toán token và chi phí → Trừ R-Points → Upload file training → Tạo fine-tuning job → <PERSON><PERSON><PERSON> thông tin vào database", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFineTuningJobDto"}}}}, "responses": {"201": {"description": "Fine-tuning job đã đ<PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "Fine-tuning job đã đ<PERSON><PERSON><PERSON> tạo thành công"}, "result": {"$ref": "#/components/schemas/CreateFineTuningJobResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"CreateUserKeyLlmDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> danh cho key", "example": "My OpenAI Key", "maxLength": 255}, "provider": {"type": "string", "enum": ["OPENAI", "ANTHROPIC", "GOOGLE", "AZURE", "COHERE", "MISTRAL", "PERPLEXITY", "GROQ"], "description": "Nhà cung cấp LLM", "example": "OPENAI"}, "apiKey": {"type": "string", "description": "API key cá nhân hóa", "example": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"}}, "required": ["name", "provider", "<PERSON><PERSON><PERSON><PERSON>"]}, "UpdateUserKeyLlmDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> danh cho key", "example": "My OpenAI Key Updated", "maxLength": 255}, "apiKey": {"type": "string", "description": "API key cá nhân hóa (n<PERSON><PERSON> muốn thay đổi)", "example": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"}}}, "CreateUserKeyLlmResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của user key LLM đ<PERSON><PERSON><PERSON> tạo", "example": "123e4567-e89b-12d3-a456-************"}, "connectionError": {"type": "string", "description": "Lỗi connection test nếu có", "example": "API key kh<PERSON>ng hợp lệ", "nullable": true}, "modelDiscovery": {"type": "object", "description": "Thông tin model discovery", "properties": {"totalModelsFound": {"type": "integer", "example": 15}, "modelsMatched": {"type": "integer", "example": 12}, "newModelsCreated": {"type": "integer", "example": 8}, "existingModelsFound": {"type": "integer", "example": 4}, "mappingsCreated": {"type": "integer", "example": 12}, "discoveryTime": {"type": "integer", "example": 1640995200000}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Discovery thành công: 8 models mới, 4 models đã tồn tại"}, "errors": {"type": "array", "items": {"type": "string"}, "example": []}}, "nullable": true}}, "required": ["id"]}, "UpdateUserKeyLlmResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của user key LLM đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "example": "123e4567-e89b-12d3-a456-************"}, "connectionError": {"type": "string", "description": "Lỗi connection test nếu có", "example": "API key kh<PERSON>ng hợp lệ", "nullable": true}, "modelDiscovery": {"type": "object", "description": "Thông tin model discovery (chỉ có khi cập nhật API key)", "properties": {"totalModelsFound": {"type": "integer", "example": 15}, "modelsMatched": {"type": "integer", "example": 12}, "newModelsCreated": {"type": "integer", "example": 3}, "existingModelsFound": {"type": "integer", "example": 9}, "mappingsCreated": {"type": "integer", "example": 12}, "discoveryTime": {"type": "integer", "example": 1640995200000}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Discovery thành công: 3 models mới, 9 models đã tồn tại"}, "errors": {"type": "array", "items": {"type": "string"}, "example": []}}, "nullable": true}}, "required": ["id"]}, "ReloadModelsResponseDto": {"type": "object", "properties": {"modelDiscovery": {"type": "object", "description": "Thông tin model discovery", "properties": {"totalModelsFound": {"type": "integer", "example": 15}, "modelsMatched": {"type": "integer", "example": 12}, "newModelsCreated": {"type": "integer", "example": 2}, "existingModelsFound": {"type": "integer", "example": 10}, "mappingsCreated": {"type": "integer", "example": 12}, "discoveryTime": {"type": "integer", "example": 1640995200000}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Reload thành công: 2 models mới, 10 models đã tồn tại"}, "errors": {"type": "array", "items": {"type": "string"}, "example": []}}}}, "required": ["modelDiscovery"]}, "PaginatedUserKeyLlmResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/UserKeyLlmResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số user key LLM", "example": 25}, "itemCount": {"type": "integer", "description": "Số user key <PERSON>M trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số user key LLM mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "UserKeyLlmResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID của user key LLM", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> danh cho key", "example": "My OpenAI Key"}, "provider": {"type": "string", "enum": ["OPENAI", "ANTHROPIC", "GOOGLE", "AZURE", "COHERE", "MISTRAL", "PERPLEXITY", "GROQ"], "description": "Nhà cung cấp LLM", "example": "OPENAI"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (epoch millis)", "example": 1640995200000}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nh<PERSON> (epoch millis)", "example": 1640995200000}}, "required": ["id", "name", "provider", "createdAt", "updatedAt"]}, "PaginatedUserModelsResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/UserModelsResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số user models", "example": 50}, "itemCount": {"type": "integer", "description": "Số user models trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số user models mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 5}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "UserModelsResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của user model", "example": "123e4567-e89b-12d3-a456-************"}, "modelId": {"type": "string", "description": "ID đ<PERSON>nh danh của model", "example": "gpt-4-turbo"}, "provider": {"type": "string", "enum": ["OPENAI", "ANTHROPIC", "GOOGLE", "AZURE", "COHERE", "MISTRAL", "PERPLEXITY", "GROQ"], "description": "Nhà cung cấp model", "example": "OPENAI"}, "modelNamePattern": {"type": "string", "description": "<PERSON><PERSON><PERSON> mẫu đại diện của model", "example": "gpt-4*"}, "inputModalities": {"type": "array", "items": {"type": "string", "enum": ["TEXT", "IMAGE", "AUDIO", "VIDEO"]}, "description": "<PERSON><PERSON><PERSON> lo<PERSON>i dữ liệu đầu vào hỗ trợ", "example": ["TEXT", "IMAGE"]}, "outputModalities": {"type": "array", "items": {"type": "string", "enum": ["TEXT", "IMAGE", "AUDIO", "VIDEO"]}, "description": "<PERSON><PERSON><PERSON> lo<PERSON>i dữ liệu đầu ra hỗ trợ", "example": ["TEXT"]}, "samplingParameters": {"type": "array", "items": {"type": "string", "enum": ["TEMPERATURE", "TOP_P", "TOP_K", "MAX_TOKENS", "FREQUENCY_PENALTY", "PRESENCE_PENALTY"]}, "description": "<PERSON><PERSON><PERSON> tham số sampling hỗ trợ", "example": ["TEMPERATURE", "TOP_P", "MAX_TOKENS"]}, "features": {"type": "array", "items": {"type": "string", "enum": ["FUNCTION_CALLING", "TOOL_USE", "JSON_MODE", "STREAMING", "VISION"]}, "description": "<PERSON><PERSON><PERSON> hợp feature đặc biệt", "example": ["FUNCTION_CALLING", "JSON_MODE"]}, "basePricing": {"type": "object", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản của model", "properties": {"inputTokenPrice": {"type": "number", "example": 0.01}, "outputTokenPrice": {"type": "number", "example": 0.03}, "currency": {"type": "string", "example": "USD"}}}, "fineTunePricing": {"type": "object", "description": "Giá fine-tune của model", "nullable": true}, "trainingPricing": {"type": "object", "description": "Giá training của model", "nullable": true}}, "required": ["id", "modelId", "provider", "modelNamePattern", "inputModalities", "outputModalities", "samplingParameters", "features", "basePricing"]}, "PaginatedSystemModelsResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/SystemModelsResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số system models", "example": 30}, "itemCount": {"type": "integer", "description": "Số system models trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số system models mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "SystemModelsResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của system model", "example": "123e4567-e89b-12d3-a456-************"}, "modelId": {"type": "string", "description": "ID đ<PERSON>nh danh của model", "example": "gpt-4-turbo"}, "provider": {"type": "string", "enum": ["OPENAI", "ANTHROPIC", "GOOGLE", "AZURE", "COHERE", "MISTRAL", "PERPLEXITY", "GROQ"], "description": "Nhà cung cấp model", "example": "OPENAI"}, "modelNamePattern": {"type": "string", "description": "<PERSON><PERSON><PERSON> mẫu đại diện của model", "example": "gpt-4*"}, "inputModalities": {"type": "array", "items": {"type": "string", "enum": ["TEXT", "IMAGE", "AUDIO", "VIDEO"]}, "description": "<PERSON><PERSON><PERSON> lo<PERSON>i dữ liệu đầu vào hỗ trợ", "example": ["TEXT", "IMAGE"]}, "outputModalities": {"type": "array", "items": {"type": "string", "enum": ["TEXT", "IMAGE", "AUDIO", "VIDEO"]}, "description": "<PERSON><PERSON><PERSON> lo<PERSON>i dữ liệu đầu ra hỗ trợ", "example": ["TEXT"]}, "samplingParameters": {"type": "array", "items": {"type": "string", "enum": ["TEMPERATURE", "TOP_P", "TOP_K", "MAX_TOKENS", "FREQUENCY_PENALTY", "PRESENCE_PENALTY"]}, "description": "<PERSON><PERSON><PERSON> tham số sampling hỗ trợ", "example": ["TEMPERATURE", "TOP_P", "MAX_TOKENS"]}, "features": {"type": "array", "items": {"type": "string", "enum": ["FUNCTION_CALLING", "TOOL_USE", "JSON_MODE", "STREAMING", "VISION"]}, "description": "<PERSON><PERSON><PERSON> hợp feature đặc biệt", "example": ["FUNCTION_CALLING", "JSON_MODE"]}, "basePricing": {"type": "object", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản của model", "properties": {"inputTokenPrice": {"type": "number", "example": 0.01}, "outputTokenPrice": {"type": "number", "example": 0.03}, "currency": {"type": "string", "example": "USD"}}}, "fineTunePricing": {"type": "object", "description": "Giá fine-tune của model", "nullable": true}, "trainingPricing": {"type": "object", "description": "Giá training của model", "nullable": true}, "active": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động của model", "example": true}}, "required": ["id", "modelId", "provider", "modelNamePattern", "inputModalities", "outputModalities", "samplingParameters", "features", "basePricing", "active"]}, "PaginatedUserModelFineTuneResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/UserModelFineTuneResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số user model fine-tune", "example": 15}, "itemCount": {"type": "integer", "description": "Số user model fine-tune trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số user model fine-tune mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 2}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "UserModelFineTuneResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của user model fine-tune", "example": "123e4567-e89b-12d3-a456-************"}, "modelId": {"type": "string", "description": "ID đ<PERSON>nh danh của fine-tuned model", "example": "ft:gpt-3.5-turbo:my-org:custom_suffix:id"}, "baseModelId": {"type": "string", "description": "ID của model cơ sở", "example": "gpt-3.5-turbo"}, "datasetName": {"type": "string", "description": "Tên dataset đã sử dụng", "example": "Customer Service Training Dataset"}, "provider": {"type": "string", "enum": ["OPENAI", "GOOGLE"], "description": "<PERSON>hà cung cấp AI", "example": "OPENAI"}, "status": {"type": "string", "enum": ["PENDING", "RUNNING", "SUCCEEDED", "FAILED", "CANCELLED"], "description": "Tr<PERSON>ng thái fine-tuning job", "example": "SUCCEEDED"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (epoch millis)", "example": 1640995200000}, "finishedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian hoàn thành (epoch millis)", "example": 1640998800000, "nullable": true}}, "required": ["id", "modelId", "baseModelId", "datasetName", "provider", "status", "createdAt"]}, "CreateUserDataFineTuneDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON>n c<PERSON>a bộ dữ liệu fine-tune", "example": "Customer Service Training Dataset", "maxLength": 255}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về bộ dữ liệu", "example": "<PERSON><PERSON> dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng", "nullable": true}, "trainDatasetMime": {"type": "string", "description": "MIME type của file training dataset", "example": "application/json"}, "validDatasetMime": {"type": "string", "description": "MIME type của file validation dataset", "example": "application/json", "nullable": true}}, "required": ["name", "trainDatasetMime"]}, "UpdateUserDataFineTuneDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON>n c<PERSON>a bộ dữ liệu fine-tune", "example": "Updated Customer Service Dataset", "maxLength": 255}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về bộ dữ liệu", "example": "<PERSON><PERSON> dữ liệu huấn luyện cập nhật cho chatbot hỗ trợ khách hàng", "nullable": true}}}, "PaginatedUserDataFineTuneResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/UserDataFineTuneResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số datasets", "example": 20}, "itemCount": {"type": "integer", "description": "Số datasets trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số datasets mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 2}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "UserDataFineTuneResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của bộ dữ liệu", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> bộ dữ liệu", "example": "Customer Service Training Dataset"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về bộ dữ liệu", "example": "<PERSON><PERSON> dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng", "nullable": true}, "status": {"type": "string", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"], "description": "<PERSON>r<PERSON><PERSON> thái của bộ dữ liệu", "example": "COMPLETED"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (epoch timestamp)", "example": 1703980800000}}, "required": ["id", "name", "status", "createdAt"]}, "UserDataFineTuneDetailResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của bộ dữ liệu", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> bộ dữ liệu", "example": "Customer Service Training Dataset"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về bộ dữ liệu", "example": "<PERSON><PERSON> dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng", "nullable": true}, "status": {"type": "string", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"], "description": "<PERSON>r<PERSON><PERSON> thái của bộ dữ liệu", "example": "COMPLETED"}, "trainDatasetUrl": {"type": "string", "description": "Đường dẫn đến tập dữ liệu huấn luyện", "example": "https://s3.amazonaws.com/bucket/path/to/dataset.jsonl"}, "validDatasetUrl": {"type": "string", "description": "Đường dẫn đến tập dữ liệu validation", "example": "https://s3.amazonaws.com/bucket/path/to/validation.jsonl", "nullable": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (epoch timestamp)", "example": 1703980800000}}, "required": ["id", "name", "status", "trainDatasetUrl", "createdAt"]}, "CreateFineTuningJobDto": {"type": "object", "properties": {"datasetId": {"type": "string", "format": "uuid", "description": "UUID của dataset để fine-tune", "example": "123e4567-e89b-12d3-a456-************"}, "baseModelId": {"type": "string", "format": "uuid", "description": "UUID của model cơ sở để fine-tune", "example": "123e4567-e89b-12d3-a456-************"}, "provider": {"type": "string", "enum": ["OPENAI", "GOOGLE"], "description": "<PERSON>hà cung cấp AI", "example": "OPENAI"}, "userKeyLlmId": {"type": "string", "format": "uuid", "description": "UUID của user key LLM (t<PERSON><PERSON> chọn - n<PERSON>u user muốn sử dụng key riêng)", "example": "123e4567-e89b-12d3-a456-************", "nullable": true}, "suffix": {"type": "string", "description": "Suffix cho tên model (chỉ cho OpenAI)", "example": "my-model", "maxLength": 40, "nullable": true}, "hyperparameters": {"type": "object", "description": "Siêu tham số cho quá trình fine-tuning", "additionalProperties": true, "example": {"epochs": 3, "batchSize": "auto", "learningRate": 0.0001}, "nullable": true}}, "required": ["datasetId", "baseModelId", "provider"]}, "CreateFineTuningJobResponseDto": {"type": "object", "properties": {"jobId": {"type": "string", "description": "ID của fine-tuning job từ provider", "example": "ftjob-abc123"}, "userModelFineTuneId": {"type": "string", "format": "uuid", "description": "ID của user model fine-tune trong hệ thống", "example": "123e4567-e89b-12d3-a456-************"}, "provider": {"type": "string", "enum": ["OPENAI", "GOOGLE"], "description": "<PERSON>hà cung cấp AI", "example": "OPENAI"}, "status": {"type": "string", "enum": ["PENDING", "RUNNING", "SUCCEEDED", "FAILED", "CANCELLED"], "description": "<PERSON>r<PERSON><PERSON> thái ban đ<PERSON>u của job", "example": "PENDING"}, "estimatedTokens": {"type": "integer", "description": "S<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "example": 50000}, "costDeducted": {"type": "number", "description": "Số R-Points đã trừ", "example": 100}, "remainingBalance": {"type": "number", "description": "Số dư R-Points còn lại", "example": 9900}, "userKeyLlmId": {"type": "string", "format": "uuid", "description": "ID của user key LLM đã sử dụng (nếu có)", "example": "123e4567-e89b-12d3-a456-************", "nullable": true}, "userKeyLlmName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> user key LLM đã sử dụng (nếu có)", "example": "My OpenAI Key", "nullable": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo job (epoch millis)", "example": 1640995200000}}, "required": ["jobId", "userModelFineTuneId", "provider", "status", "estimatedTokens", "costDeducted", "remainingBalance", "createdAt"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 500}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}