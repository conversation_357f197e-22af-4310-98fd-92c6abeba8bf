# RedAI Affiliate MCP Server

Server MCP cho RedAI Affiliate Module API sử dụng FastMCP và OpenAPI Schema. Server này tự động tạo các MCP tools và resources từ `swagger.json`, cung cấp giao diện MCP cho tất cả các endpoint của Affiliate Module API.

## Tính năng

- ✅ **Tự động tạo tools từ OpenAPI specification**
- 🔑 **Hỗ trợ Bearer token authentication từ client**
- 🚀 **Streamable HTTP transport với FastMCP**
- 🔧 **Tùy chỉnh route mapping cho các loại endpoint khác nhau**
- 📊 **Xử lý parameters, headers và request body tự động**
- 💰 **Tools tùy chỉnh cho affiliate functionality**

## Cấu trúc API

### Statistics (Thống kê)
- `GET /user/affiliate/statistics` - Lấy thông tin thống kê tài khoản affiliate

### Account (<PERSON><PERSON><PERSON>)
- `GET /user/affiliate/account` - <PERSON><PERSON><PERSON> thông tin tài khoản affiliate của người dùng

### Withdrawals (<PERSON><PERSON><PERSON> tiền)
- `GET /user/affiliate/withdrawals` - Lấy danh sách yêu cầu rút tiền
- `POST /user/affiliate/withdrawals` - Tạo yêu cầu rút tiền

### Referral Links (Liên kết giới thiệu)
- `GET /user/affiliate/referral-links` - Lấy danh sách liên kết giới thiệu

### Business (Doanh nghiệp)
- `GET /user/affiliate/business` - Lấy thông tin doanh nghiệp
- `POST /user/affiliate/business` - Tạo thông tin doanh nghiệp
- `PATCH /user/affiliate/business` - Cập nhật thông tin doanh nghiệp

## Cấu hình

### Biến môi trường

```bash
# API Configuration
REDAI_AFFILIATE_API_BASE_URL=https://api.redai.com

# Server Configuration
AFFILIATE_HTTP_HOST=127.0.0.1
AFFILIATE_HTTP_PORT=8004
AFFILIATE_HTTP_PATH=/mcp
AFFILIATE_TRANSPORT=streamable-http
```

### Authentication

Server sử dụng Bearer token authentication:
- Bearer token được truyền từ client khi kết nối
- Tự động xử lý authentication cho tất cả requests
- Sử dụng tool `update_bearer_token` để cấu hình authentication

## Cài đặt và chạy

### 1. Cài đặt dependencies

```bash
pip install fastmcp>=2.3.0 httpx
# hoặc
pip install -e .
```

### 2. Chạy server

```bash
# Chạy với Streamable HTTP transport (mặc định)
python src/server/redai_system/affiliatte/affiliate_server.py

# Chạy với transport cụ thể
python src/server/redai_system/affiliatte/affiliate_server.py streamable-http
python src/server/redai_system/affiliatte/affiliate_server.py sse
python src/server/redai_system/affiliatte/affiliate_server.py stdio
```

### 3. Kiểm tra server

```bash
# Chạy test script
python test_affiliate_server.py
```

Server sẽ khởi động tại:
- **MCP Endpoint**: `http://127.0.0.1:8004/mcp`
- **Server URL**: `http://127.0.0.1:8004`

## Sử dụng

### Kết nối từ MCP Client

```python
from fastmcp import Client

async def main():
    # Kết nối qua HTTP
    async with Client("http://127.0.0.1:8004/mcp") as client:
        # Cập nhật authentication
        await client.call_tool("update_bearer_token", {
            "bearer_token": "your_bearer_token_here"
        })
        
        # Lấy thống kê affiliate
        stats = await client.call_tool("get_user_affiliate_statistics", {
            "startDate": "2024-01-01",
            "endDate": "2024-12-31"
        })
        
        # Lấy thông tin tài khoản
        account = await client.call_tool("get_user_affiliate_account", {})
        
        # Tạo yêu cầu rút tiền
        withdrawal = await client.call_tool("post_user_affiliate_withdrawals", {
            "amount": 1000000,
            "bankAccount": "**********",
            "bankName": "Vietcombank",
            "accountHolderName": "Nguyễn Văn A"
        })
```

### Tools có sẵn

#### Authentication Tools
- `update_bearer_token` - Cập nhật Bearer token cho authentication
- `check_auth_status` - Kiểm tra trạng thái authentication hiện tại

#### Affiliate Tools
- `get_affiliate_summary` - Lấy tổng quan về affiliate của người dùng
- `calculate_commission` - Tính toán hoa hồng affiliate

#### API Tools (tự động tạo từ OpenAPI)
- `get_user_affiliate_statistics` - Lấy thống kê affiliate
- `get_user_affiliate_account` - Lấy thông tin tài khoản
- `get_user_affiliate_withdrawals` - Lấy danh sách rút tiền
- `post_user_affiliate_withdrawals` - Tạo yêu cầu rút tiền
- `get_user_affiliate_referral_links` - Lấy liên kết giới thiệu
- `get_user_affiliate_business` - Lấy thông tin doanh nghiệp
- `post_user_affiliate_business` - Tạo thông tin doanh nghiệp
- `patch_user_affiliate_business` - Cập nhật thông tin doanh nghiệp

### Ví dụ sử dụng tools

```python
# Tính toán hoa hồng
commission = await client.call_tool("calculate_commission", {
    "amount": 1000000,
    "commission_rate": 5.5
})

# Lấy tổng quan affiliate
summary = await client.call_tool("get_affiliate_summary", {})

# Kiểm tra authentication
auth_status = await client.call_tool("check_auth_status", {})
```

## Cấu hình MCP Client

### Claude Desktop

Thêm vào file cấu hình MCP:

```json
{
  "mcpServers": {
    "redai-affiliate": {
      "command": "python",
      "args": ["src/server/redai_system/affiliatte/affiliate_server.py"],
      "env": {
        "REDAI_AFFILIATE_API_BASE_URL": "https://api.redai.com",
        "AFFILIATE_HTTP_HOST": "127.0.0.1",
        "AFFILIATE_HTTP_PORT": "8004"
      }
    }
  }
}
```

### Codeium/Continue

```json
{
  "mcpServers": {
    "redai-affiliate": {
      "url": "http://127.0.0.1:8004/mcp"
    }
  }
}
```

## Troubleshooting

### Lỗi thường gặp

**Lỗi**: `FileNotFoundError: Không tìm thấy file schema tại: swagger.json`
- **Giải pháp**: Đảm bảo file `swagger.json` tồn tại trong thư mục `affiliatte/`

**Lỗi**: `ImportError: No module named 'fastmcp'`
- **Giải pháp**: Cài đặt dependencies: `pip install fastmcp>=2.3.0`

**Lỗi**: `ConnectionError` khi gọi API
- **Giải pháp**: Kiểm tra Bearer token và API base URL

### Debug

Để debug server, sử dụng:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Phát triển

### Thêm tools mới

```python
@mcp_server.tool()
async def new_affiliate_tool(param: str) -> str:
    """Mô tả tool mới"""
    # Logic xử lý
    return "Kết quả"
```

### Cập nhật OpenAPI schema

1. Cập nhật file `swagger.json`
2. Restart server để load schema mới
3. Tools sẽ được tự động tạo từ schema mới

## Tài liệu tham khảo

- [FastMCP Documentation](https://github.com/jlowin/fastmcp)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [OpenAPI Specification](https://swagger.io/specification/)
