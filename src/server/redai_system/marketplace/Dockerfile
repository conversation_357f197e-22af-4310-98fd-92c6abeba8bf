# RedAI Marketplace MCP Server Dockerfile
# Sử dụng Python 3.11 slim image để giảm kích thước
FROM python:3.11-slim

# Metadata
LABEL maintainer="RedAI Development Team <<EMAIL>>"
LABEL description="RedAI Marketplace MCP Server - Qu<PERSON>n lý hệ thống marketplace cho người dùng"
LABEL version="1.0.0"

# Thiết lập biến môi trường
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Tạo user non-root để bảo mật
RUN groupadd -r redai && useradd -r -g redai redai

# Thiết lập working directory
WORKDIR /app

# Cài đặt system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements và cài đặt Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ ./src/
COPY *.py ./

# Copy marketplace-specific files
COPY src/server/redai_system/marketplace/ ./src/server/redai_system/marketplace/

# Tạo thư mục logs và đổi ownership
RUN mkdir -p /app/logs && \
    chown -R redai:redai /app

# Switch to non-root user
USER redai

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8008/health || exit 1

# Expose port
EXPOSE 8008

# Environment variables với default values
ENV REDAI_MARKETPLACE_API_BASE_URL=https://api.redai.com \
    MARKETPLACE_HTTP_HOST=0.0.0.0 \
    MARKETPLACE_HTTP_PORT=8008 \
    MARKETPLACE_HTTP_PATH=/mcp \
    MARKETPLACE_TRANSPORT=streamable-http

# Command để chạy server
CMD ["python", "src/server/redai_system/marketplace/marketplace_server.py"]
